import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface LayoutConfig {
  key: string;
  name: string;
  hasHeader: boolean;
  hasLeftSidebar: boolean;
  hasRightSidebar: boolean;
  hasFooter: boolean;
}

@Component({
  selector: 'app-fluid-layout-animation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './fluid-layout-animation.component.html',
  styleUrls: ['./fluid-layout-animation.component.scss']
})
export class FluidLayoutAnimationComponent implements OnInit, OnChanges {
  @Input() layoutKey: string = 'HB';
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() animated: boolean = true;
  @Input() showTitle: boolean = true;
  @Input() showDescription: boolean = true;

  layoutConfig: LayoutConfig = {
    key: 'HB',
    name: 'Header + Body',
    hasHeader: true,
    hasLeftSidebar: false,
    hasRightSidebar: false,
    hasFooter: false
  };

  private layoutMapping: { [key: string]: LayoutConfig } = {
    HB: {
      key: 'HB',
      name: 'Header + Body',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: false,
      hasFooter: false
    },
    HBF: {
      key: 'HBF',
      name: 'Header + Body + Footer',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: false,
      hasFooter: true
    },
    HLSB: {
      key: 'HLSB',
      name: 'Header + Left Sidebar + Body',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: false,
      hasFooter: false
    },
    HLSBF: {
      key: 'HLSBF',
      name: 'Header + Left Sidebar + Body + Footer',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: false,
      hasFooter: true
    },
    HBRS: {
      key: 'HBRS',
      name: 'Header + Body + Right Sidebar',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: true,
      hasFooter: false
    },
    HBRSF: {
      key: 'HBRSF',
      name: 'Header + Body + Right Sidebar + Footer',
      hasHeader: true,
      hasLeftSidebar: false,
      hasRightSidebar: true,
      hasFooter: true
    },
    HLSBRS: {
      key: 'HLSBRS',
      name: 'Header + Left + Body + Right Sidebar',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: true,
      hasFooter: false
    },
    HLSBRSF: {
      key: 'HLSBRSF',
      name: 'Complete Layout',
      hasHeader: true,
      hasLeftSidebar: true,
      hasRightSidebar: true,
      hasFooter: true
    }
  };

  ngOnInit(): void {
    this.updateLayoutConfig();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['layoutKey']) {
      this.updateLayoutConfig();
    }
  }

  private updateLayoutConfig(): void {
    this.layoutConfig = this.layoutMapping[this.layoutKey] || this.layoutMapping['HB'];
  }

  getLayoutDescription(): string {
    const config = this.layoutConfig;
    if (!config.hasLeftSidebar && !config.hasRightSidebar && !config.hasFooter) {
      return 'Clean and minimal layout with just header and main content';
    } else if (config.hasLeftSidebar && !config.hasRightSidebar && !config.hasFooter) {
      return 'Navigation-focused layout with left sidebar for menus';
    } else if (!config.hasLeftSidebar && config.hasRightSidebar && !config.hasFooter) {
      return 'Content-first layout with right sidebar for additional info';
    } else if (config.hasLeftSidebar && config.hasRightSidebar && !config.hasFooter) {
      return 'Comprehensive layout with dual sidebars for maximum functionality';
    } else if (config.hasFooter && !config.hasLeftSidebar && !config.hasRightSidebar) {
      return 'Traditional three-section layout with header, content, and footer';
    } else if (config.hasLeftSidebar && config.hasRightSidebar && config.hasFooter) {
      return 'Full-featured layout with all sections for complex applications';
    } else {
      return 'Balanced layout with multiple sections';
    }
  }
}
