// Base layout card styles
.layout-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  height: 100%;
  min-height: 300px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.theme-dark {
    background: rgba(30, 30, 30, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  &.animated:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.95);

    &.theme-dark {
      background: rgba(30, 30, 30, 0.95);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    }

    .layout-preview {
      transform: scale(1.05);
    }
  }
}

.layout-title {
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;

  .theme-dark & {
    color: #e9ecef;
  }
}

.layout-description {
  color: #6c757d;
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1.5rem;

  .theme-dark & {
    color: #adb5bd;
  }
}

.layout-preview {
  width: 100%;
  height: 200px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;

  .theme-dark & {
    background: #212529;
    border: 1px solid #495057;
  }
}

.layout-element {
  position: absolute;
  border-radius: 6px;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header {
  background: #c7d2fe;
  border: 1px solid #a5b4fc;
  top: 10px;
  left: 10px;
  right: 10px;
  height: 30px;
  box-shadow: 0 2px 8px rgba(167, 180, 252, 0.2);

  .theme-dark & {
    background: #4c1d95;
    border: 1px solid #6d28d9;
    box-shadow: 0 2px 8px rgba(109, 40, 217, 0.3);
  }
}

.left-sidebar {
  background: #e0e7ff;
  border: 1px solid #c7d2fe;
  top: 50px;
  left: 10px;
  width: 60px;
  bottom: 10px;
  box-shadow: 0 2px 8px rgba(199, 210, 254, 0.2);

  .theme-dark & {
    background: #312e81;
    border: 1px solid #4c1d95;
    box-shadow: 0 2px 8px rgba(76, 29, 149, 0.3);
  }

  .animated & {
    animation: sidebarPulse 3s ease-in-out infinite;
  }
}

.right-sidebar {
  background: #e0e7ff;
  border: 1px solid #c7d2fe;
  top: 50px;
  right: 10px;
  width: 60px;
  bottom: 10px;
  box-shadow: 0 2px 8px rgba(199, 210, 254, 0.2);

  .theme-dark & {
    background: #312e81;
    border: 1px solid #4c1d95;
    box-shadow: 0 2px 8px rgba(76, 29, 149, 0.3);
  }

  .animated & {
    animation: sidebarPulse 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }
}

.body {
  background: #ffffff;
  border: 1px solid #e9ecef;
  top: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  .theme-dark & {
    background: #343a40;
    border: 1px solid #6c757d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.footer {
  background: #f1f3f4;
  border: 1px solid #dee2e6;
  bottom: 10px;
  left: 10px;
  right: 10px;
  height: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .theme-dark & {
    background: #495057;
    border: 1px solid #6c757d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

// Sidebar Animation Keyframes
@keyframes sidebarPulse {
  0%, 100% { 
    width: 60px; 
    opacity: 0.8;
  }
  25% { 
    width: 20px; 
    opacity: 0.4;
  }
  50% { 
    width: 5px; 
    opacity: 0.2;
  }
  75% { 
    width: 35px; 
    opacity: 0.6;
  }
}

@keyframes bodyFluid {
  0%, 100% { 
    left: 80px; 
  }
  25% { 
    left: 40px; 
  }
  50% { 
    left: 25px; 
  }
  75% { 
    left: 55px; 
  }
}

@keyframes bodyFluidRight {
  0%, 100% { 
    right: 80px; 
  }
  25% { 
    right: 40px; 
  }
  50% { 
    right: 25px; 
  }
  75% { 
    right: 55px; 
  }
}

@keyframes bodyFluidBoth {
  0%, 100% { 
    left: 80px; 
    right: 80px; 
  }
  25% { 
    left: 40px; 
    right: 40px; 
  }
  50% { 
    left: 25px; 
    right: 25px; 
  }
  75% { 
    left: 55px; 
    right: 55px; 
  }
}

// Layout-specific body positioning and animations
// HB Layout
.hb .body {
  left: 10px;
  right: 10px;
  bottom: 10px;
}

// HBF Layout
.hbf .body {
  left: 10px;
  right: 10px;
  bottom: 45px;
}

// HLSB Layout - Animated
.hlsb .body {
  left: 80px;
  right: 10px;
  bottom: 10px;

  .animated & {
    animation: bodyFluid 3s ease-in-out infinite;
  }
}

// HLSBF Layout - Animated
.hlsbf .body {
  left: 80px;
  right: 10px;
  bottom: 45px;

  .animated & {
    animation: bodyFluid 3s ease-in-out infinite;
  }
}

// HBRS Layout - Animated
.hbrs .body {
  left: 10px;
  right: 80px;
  bottom: 10px;

  .animated & {
    animation: bodyFluidRight 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }
}

// HBRSF Layout - Animated
.hbrsf .body {
  left: 10px;
  right: 80px;
  bottom: 45px;

  .animated & {
    animation: bodyFluidRight 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }
}

// HLSBRS Layout - Animated
.hlsbrs .body {
  left: 80px;
  right: 80px;
  bottom: 10px;

  .animated & {
    animation: bodyFluidBoth 3s ease-in-out infinite;
  }
}

// HLSBRSF Layout - Animated
.hlsbrsf .body {
  left: 80px;
  right: 80px;
  bottom: 45px;

  .animated & {
    animation: bodyFluidBoth 3s ease-in-out infinite;
  }
}

// Hover effects for animated layouts
.animated.layout-card:hover .layout-element {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.animated.layout-card:hover .header {
  animation-delay: 0s;
}

.animated.layout-card:hover .left-sidebar {
  animation-delay: 0.2s;
}

.animated.layout-card:hover .body {
  animation-delay: 0.4s;
}

.animated.layout-card:hover .right-sidebar {
  animation-delay: 0.6s;
}

.animated.layout-card:hover .footer {
  animation-delay: 0.8s;
}

// Glow effect
.glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(199, 210, 254, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;

  .theme-dark & {
    background: radial-gradient(circle, rgba(109, 40, 217, 0.2) 0%, transparent 70%);
  }
}

.animated.layout-card:hover .glow {
  opacity: 1;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .layout-card {
    min-height: 250px;
    padding: 1rem;
  }
  
  .layout-preview {
    height: 150px;
  }
  
  .layout-title {
    font-size: 1rem;
  }
  
  .layout-description {
    font-size: 0.8rem;
  }
}
