import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  OnDestroy,
  Output,
  EventEmitter,
  ChangeDetectorRef,
} from '@angular/core';
import { MarkdownModule } from 'ngx-markdown';
import { StepperState, StepperStateDisplayTitles } from '../../models/stepper-states.enum';
import { PollingService } from '../../services/polling.service';
import { createLogger } from '../../utils/logger';
import { SubscriptionManager } from '../../utils/subscription-management.util';

export interface StepperItem {
  title: string;
  description: string;
  visibleDescription: string; // Property to track the visible portion of the description for typewriter effect
  completed: boolean;
  active: boolean;

  collapsed?: boolean; // Property to track collapsed state
  isTyping?: boolean; // Property to track if the description is currently being typed
  retryCount?: number; // Property to track retry attempts for failed steps
  isRetrying?: boolean; // Property to track if the step is currently being retried (for shimmer effect)

  // Timer properties
  startTime?: number; // Timestamp when the step started
  elapsedTime?: number; // Current elapsed time in seconds
  timerActive?: boolean; // Whether the timer is currently running
  completionTime?: number; // Final time when the step was completed (in seconds)
}

@Component({
  selector: 'app-vertical-stepper',
  standalone: true,
  imports: [CommonModule, MarkdownModule],
  templateUrl: './vertical-stepper.component.html',
  styleUrls: ['./vertical-stepper.component.scss'],
})
export class VerticalStepperComponent implements OnChanges, OnInit, OnDestroy {
  @Input() progress: string = '';
  @Input() progressDescription: string = '';
  @Input() status: string = 'PENDING'; // PENDING, IN_PROGRESS, COMPLETED, FAILED
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() restartable: boolean = false; // Whether to show restart button
  @Input() projectId: string = ''; // Project ID for API integration
  @Input() jobId: string = ''; // Job ID for API integration
  @Input() useApi: boolean = false; // Whether to use API for data or local inputs
  @Input() jsonDataMode: boolean = false; // Whether to use JSON data mode for test.json integration
  @Input() currentStepFromJson: number = 0; // Current step index when using JSON data mode
  @Output() stepUpdated = new EventEmitter<number>(); // Emits the current step index when it changes
  @Output() retryStep = new EventEmitter<number>(); // Emits the step index when retry is clicked

  private logger = createLogger('VerticalStepperComponent');

  steps: StepperItem[] = [];
  currentStep: StepperItem | null = null;
  currentStepIndex: number = 0;
  animatingLine: boolean = false;
  private timeoutRefs: { [key: string]: any } = {};
  private subscriptionManager = new SubscriptionManager();
  private typingSpeed: number = 20; // Speed of typewriter effect in milliseconds per character

  // Timer-related properties
  private timerInterval: any;
  private timerUpdateInterval: number = 1000; // Update timer every second

  // Track which steps are collapsed
  private collapsedSteps: Set<number> = new Set();

  // Map of step states to their display titles
  private stepperStateMap = StepperStateDisplayTitles;

  // Expose StepperState enum to the template
  public StepperState = StepperState;

  constructor(private pollingService: PollingService, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    // If using API and we have project and job IDs, start polling
    if (this.useApi && this.projectId && this.jobId) {
      this.startApiPolling();
    }
    // Otherwise, initialize with default step if none exists and we have progress
    else if (this.steps.length === 0 && this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];
      this.currentStepIndex = 0;

      // By default, expand the active step and collapse others
      // First, collapse all steps except the processing step
      const processingStepIndex = this.status === 'IN_PROGRESS' ? this.currentStepIndex : -1;
      for (let i = 0; i < this.steps.length; i++) {
        // Don't collapse the processing step
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      // Then expand only the current step
      this.collapsedSteps.delete(this.currentStepIndex);

      // Emit the step updated event
      this.stepUpdated.emit(this.currentStepIndex);

      // Start the typewriter animation for the initial step after a short delay
      // to ensure the component is fully rendered
      setTimeout(() => {
        this.startTypewriterAnimation(this.currentStepIndex);
      }, 100);

      // Start the timer for the initial step
      this.startTimer();
    }

    // Initialize any existing steps that might have been passed in
    for (let i = 0; i < this.steps.length; i++) {
      const step = this.steps[i];
      if (!step.visibleDescription) {
        step.visibleDescription = '';
        step.isTyping = true;

        // Start typewriter animation for each step with a staggered delay
        setTimeout(() => {
          this.startTypewriterAnimation(i);
        }, 100 * (i + 1));
      }
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // If using API and project or job ID changes, restart polling
    if (this.useApi && (changes['projectId'] || changes['jobId'])) {
      if (this.projectId && this.jobId) {
        // Stop existing polling if any
        this.stopApiPolling();
        // Start new polling
        this.startApiPolling();
      }
    }
    // When not using API and progress or description changes, update the stepper
    else if (!this.useApi && (changes['progress'] || changes['progressDescription'] || changes['status'])) {
      this.updateStepper();
    }

    // Special handling for progress description changes in failed state
    if (changes['progressDescription'] && this.status === 'FAILED') {
      this.updateFailedStepDescription();
    }
  }

  /**
   * Updates the description of the failed step when the progress description changes
   * This ensures that the error message is always up to date
   */
  private updateFailedStepDescription(): void {
    // Find the failed step
    const failedStepIndex = this.steps.findIndex(step => this.isFailureStep(step));
    if (failedStepIndex >= 0) {
      // Extract error message from the progress description or log field
      let errorMessage = '';

      // First try to get the error message from the progress description
      if (this.progressDescription && this.progressDescription.trim() !== '') {
        errorMessage = this.formatDescription(this.progressDescription);
      }

      // If we couldn't get an error message from the progress description,
      // try to get it from the polling service's last status response
      if (!errorMessage && this.useApi) {
        const lastResponse = this.pollingService.getLastStatusResponse();
        if (lastResponse && lastResponse.details && lastResponse.details.log) {
          // Extract error message from the log field
          const logContent = lastResponse.details.log;
          errorMessage = this.extractErrorMessage(logContent);
        }
      }

      // If we have an error message, update the failed step description
      if (errorMessage) {
        const failedStep = this.steps[failedStepIndex];

        // Only update if the description has changed
        if (failedStep.description !== errorMessage) {
          failedStep.description = errorMessage;
          failedStep.visibleDescription = ''; // Reset visible description
          failedStep.isTyping = true; // Set typing state

          // Start typewriter animation
          this.startTypewriterAnimation(failedStepIndex);

          // Force change detection
          this.cdr.detectChanges();
        }
      }
    }
  }

  ngOnDestroy(): void {
    // Clear all timeouts when component is destroyed
    this.clearAllTimeouts();
    // Stop timer
    this.stopTimer();
    // Stop polling if active
    this.stopApiPolling();
  }

  /**
   * Start polling the API for status updates
   */
  private startApiPolling(): void {
    if (!this.projectId || !this.jobId) {
      return;
    }

    // Start polling with the polling service
    this.pollingService.startPolling(this.projectId, this.jobId);

    // Subscribe to status updates
    this.subscriptionManager.subscribe(
      this.pollingService.status$,
      status => {
        this.status = status;
      }
    );

    // Subscribe to progress updates
    this.subscriptionManager.subscribe(
      this.pollingService.progress$,
      progress => {
        if (progress) {
          this.progress = progress;
          this.updateStepper();
        }
      }
    );

    // Subscribe to progress description updates
    this.subscriptionManager.subscribe(
      this.pollingService.progressDescription$,
      description => {
        if (description) {
          this.progressDescription = description;
          this.updateStepper();
        }
      }
    );
  }

  /**
   * Stop polling the API
   */
  private stopApiPolling(): void {
    this.pollingService.stopPolling();
  }

  // Clear all timeouts
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  /**
   * Format description to ensure proper markdown and extract error messages from JSON if needed
   * @param description The description to format
   * @returns The formatted description
   */
  private formatDescription(description?: string): string {
    if (!description) return '';

    // Check if the description contains an <mlo_artifact> tag
    if (description.includes('<mlo_artifact>')) {
      // Extract only the content before the <mlo_artifact> tag
      const parts = description.split('<mlo_artifact>');
      description = parts[0];
    }

    // Check if the description is a JSON string with an error message
    if (description.includes('{') && description.includes('}') &&
        (description.includes('"message"') || description.includes('error') ||
         description.includes('Error') || description.includes('Unexpected'))) {
      try {
        // Use the extractErrorMessage method to get the error message
        return this.extractErrorMessage(description);
      } catch (e) {
        // If extraction fails, just use the original description
        this.logger.warn('Failed to extract error message from description:', e);
      }
    }

    // Return the description as is - it will be rendered as markdown
    return description;
  }

  // Format title to replace underscores with spaces
  formatTitle(title: string): string {
    return title.replace(/_/g, ' ');
  }

  // Get the status of a step
  getStepStatus(index: number): string {
    if (index < this.currentStepIndex) return 'completed';
    if (index === this.currentStepIndex) return 'active';
    if (index === this.currentStepIndex + 1) return 'next'; // Next step shows only title with low opacity
    return 'future';
  }

  // Check if a step should be shown
  shouldShowStep(index: number): boolean {
    // Show current step and next step title only
    // Current step and all completed steps
    if (index <= this.currentStepIndex) {
      return true;
    }

    // Next step (show with low opacity)
    if (index === this.currentStepIndex + 1) {
      return true;
    }

    // Hide all future steps beyond the next one
    return false;
  }

  // Check if a step's line should be animating
  isLineAnimating(index: number): boolean {
    return this.animatingLine && index === this.currentStepIndex - 1;
  }

  // Check if a step is collapsed
  isStepCollapsed(index: number): boolean {
    return this.collapsedSteps.has(index);
  }

  // Toggle the collapsed state of a step
  toggleStepCollapse(index: number): void {
    // Find the currently processing step (if any)
    const processingStepIndex = this.status === 'IN_PROGRESS' ? this.currentStepIndex : -1;

    // If the step is already expanded, collapse it (unless it's the processing step)
    if (!this.collapsedSteps.has(index)) {
      // Don't allow collapsing the processing step
      if (index !== processingStepIndex) {
        this.collapsedSteps.add(index);
      }
    } else {
      // If the step is collapsed, expand it and collapse all others except the processing step
      // First, collapse all steps except the processing step
      for (let i = 0; i < this.steps.length; i++) {
        // Don't collapse the processing step
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      // Then expand the clicked step
      this.collapsedSteps.delete(index);

      // If the step is being expanded and hasn't completed its typewriter animation,
      // restart the animation if it was interrupted
      const step = this.steps[index];
      if (step && step.description && (!step.visibleDescription || step.visibleDescription.length < step.description.length)) {
        step.isTyping = true;
        this.startTypewriterAnimation(index);
      }
    }
  }



  // Restart the stepper
  restartStepper(): void {
    this.clearAllTimeouts();
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();

    // If using API, restart polling
    if (this.useApi && this.projectId && this.jobId) {
      this.stopApiPolling();
      this.startApiPolling();
    }
    // Otherwise, if we have a progress value, initialize with it
    else if (this.progress) {
      const displayTitle = this.getDisplayTitleForProgress(this.progress);
      const description = this.formatDescription(this.progressDescription) || 'Starting process...';
      this.steps.push({
        title: displayTitle,
        description: description,
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTyping: true,
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: true
      });
      this.currentStep = this.steps[0];

      // Ensure only the first step is expanded
      // First, collapse all steps except the processing step
      const processingStepIndex = this.status === 'IN_PROGRESS' ? 0 : -1;
      for (let i = 0; i < this.steps.length; i++) {
        // Don't collapse the processing step
        if (i !== processingStepIndex) {
          this.collapsedSteps.add(i);
        }
      }
      // Then expand only the first step
      this.collapsedSteps.delete(0);

      // Emit the step updated event
      this.stepUpdated.emit(0);

      // Start the typewriter animation for the restarted step
      this.startTypewriterAnimation(0);

      // Start the timer for the restarted step
      this.startTimer();
    }
  }

  /**
   * Completely resets the stepper state
   * This is a public method that can be called from parent components
   * to reset the stepper when starting a new project
   */
  public resetStepper(): void {
    // Clear all timeouts to prevent any pending animations
    this.clearAllTimeouts();

    // Reset all state variables
    this.steps = [];
    this.currentStepIndex = 0;
    this.currentStep = null;
    this.animatingLine = false;
    this.collapsedSteps.clear();

    // Reset input properties
    this.progress = '';
    this.progressDescription = '';
    this.status = 'PENDING';

    // Stop any API polling
    if (this.useApi) {
      this.stopApiPolling();
    }

    // Emit an event to notify parent components
    this.stepUpdated.emit(-1); // -1 indicates a complete reset
  }

  private updateStepper(): void {
    // If we have a new progress state, add it as a step if it doesn't exist
    if (this.progress && this.progress.trim() !== '') {
      // Get the display title for this progress state
      const displayTitle = this.getDisplayTitleForProgress(this.progress);

      // Check if we already have this step
      const existingStepIndex = this.steps.findIndex(
        step => step.title === displayTitle || step.title === this.progress
      );

      // Special case: If progress is BUILD_FAILED, handle it through the failure logic
      // regardless of the status (could be FAILED or IN_PROGRESS)
      if (this.progress === StepperState.BUILD_FAILED) {
        this.handleFailedStep(this.progress, this.progressDescription);
        return;
      }

      // If we're in a FAILED state, handle it through the dedicated failure logic
      if (this.status === 'FAILED') {
        // Always call handleFailedStep when in FAILED state to ensure the error message is updated
        this.handleFailedStep(this.progress, this.progressDescription);
        return;
      }

      if (existingStepIndex === -1) {
        // Add new step
        const description = this.formatDescription(this.progressDescription) || '';
        const newStep: StepperItem = {
          title: displayTitle,
          description: description,
          visibleDescription: '', // Start empty for typewriter effect
          completed: false,
          active: true,
          isTyping: true,
          startTime: Date.now(),
          elapsedTime: 0,
          timerActive: true
        };

        // Mark previous step as completed and collapse it
        if (this.steps.length > 0) {
          const prevStepIndex = this.steps.length - 1;
          this.steps[prevStepIndex].completed = true;
          this.steps[prevStepIndex].active = false;
          // Stop the timer for the previous step
          this.stopStepTimer(prevStepIndex);

          // When a new step starts processing, collapse all previous steps
          // First, collapse all steps
          for (let i = 0; i < this.steps.length; i++) {
            this.collapsedSteps.add(i);
          }

          // The new step will be expanded when it's added to the steps array
          // We don't need to expand any step here as the current step index will be updated later
        }

        // Start line animation
        this.animatingLine = true;
        this.timeoutRefs['line-animation'] = setTimeout(() => {
          this.animatingLine = false;
        }, 1500);

        this.steps.push(newStep);
        this.currentStep = newStep;
        this.currentStepIndex = this.steps.length - 1;

        // Expand only the new step
        this.collapsedSteps.delete(this.currentStepIndex);

        // Emit the step updated event
        this.stepUpdated.emit(this.currentStepIndex);

        // Start the typewriter animation for the new step
        this.startTypewriterAnimation(this.currentStepIndex);

        // Start the timer if it's not already running
        if (!this.timerInterval) {
          this.startTimer();
        }

        // Ensure smooth scrolling to the new step
        setTimeout(() => {
          const stepElements = document.querySelectorAll('.stepper-item');
          if (stepElements && stepElements.length > 0) {
            const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
            if (lastStep) {
              lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
          }
        }, 100);
      } else {
        // Update existing step description if it has changed
        if (this.progressDescription && this.progressDescription.trim() !== '') {
          const newDescription = this.formatDescription(this.progressDescription);
          // Only update if the description has actually changed
          if (this.steps[existingStepIndex].description !== newDescription) {
            this.steps[existingStepIndex].description = newDescription;
            this.steps[existingStepIndex].visibleDescription = ''; // Reset visible description
            this.steps[existingStepIndex].isTyping = true; // Set typing state
            this.startTypewriterAnimation(existingStepIndex); // Start typewriter animation
          }
        }

        // Update the title to make sure it uses the display title
        this.steps[existingStepIndex].title = displayTitle;

        this.currentStep = this.steps[existingStepIndex];
        this.currentStepIndex = existingStepIndex;

        // Make sure this step is active and collapse previous steps
        for (let i = 0; i < this.steps.length; i++) {
          this.steps[i].active = i === existingStepIndex;
          this.steps[i].completed = i < existingStepIndex;
        }

        // When a step is updated and it's the current processing step, collapse all other steps
        // First, collapse all steps except the processing step
        const processingStepIndex = this.status === 'IN_PROGRESS' ? this.currentStepIndex : -1;
        for (let i = 0; i < this.steps.length; i++) {
          // Don't collapse the processing step
          if (i !== processingStepIndex) {
            this.collapsedSteps.add(i);
          }
        }
        // Then expand the current step
        this.collapsedSteps.delete(existingStepIndex);
      }
    }

    // Handle special status cases
    if (this.status === 'COMPLETED') {
      // If status is COMPLETED, mark all steps as completed and stop their timers
      this.steps.forEach((step, index) => {
        step.completed = true;
        step.active = false;
        // Stop timer and capture completion time for any active timers
        if (step.timerActive && step.startTime) {
          step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
          step.elapsedTime = step.completionTime;
          step.timerActive = false;
        }
      });

      // Check if we have a BUILD_SUCCEEDED progress
      if (this.progress === StepperState.BUILD_SUCCEEDED) {
        // For BUILD_SUCCEEDED, we want to show "Build Completed" instead of "Completed"
        const buildSucceededStepIndex = this.steps.findIndex(
          step => step.title === this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED)
        );

        if (buildSucceededStepIndex === -1 && this.steps.length > 0) {
          const description = this.formatDescription(this.progressDescription) || 'Build completed successfully.';
          const buildSucceededStep: StepperItem = {
            title: this.getDisplayTitleForProgress(StepperState.BUILD_SUCCEEDED),
            description: description,
            visibleDescription: '', // Start empty for typewriter effect
            completed: true, // Mark as completed to show checkmark
            active: true, // Make it active
            isTyping: true,
            startTime: Date.now(),
            elapsedTime: 0,
            timerActive: false // Completed steps don't need active timers
          };

          // Add the BUILD_SUCCEEDED step
          this.addFinalStep(buildSucceededStep);
          return; // Exit early to avoid adding the generic COMPLETED step
        } else if (buildSucceededStepIndex !== -1) {
          // Update the existing BUILD_SUCCEEDED step
          const step = this.steps[buildSucceededStepIndex];
          step.completed = true;
          step.active = true;

          // Update description if needed
          if (this.progressDescription && this.progressDescription.trim() !== '') {
            const newDescription = this.formatDescription(this.progressDescription);
            if (step.description !== newDescription) {
              step.description = newDescription;
              step.visibleDescription = ''; // Reset visible description
              step.isTyping = true; // Set typing state
              this.startTypewriterAnimation(buildSucceededStepIndex);
            }
          }

          // Make this the current step
          this.currentStep = step;
          this.currentStepIndex = buildSucceededStepIndex;

          // Expand only this step
          for (let i = 0; i < this.steps.length; i++) {
            this.collapsedSteps.add(i);
          }
          this.collapsedSteps.delete(buildSucceededStepIndex);

          this.stepUpdated.emit(buildSucceededStepIndex);
          return; // Exit early
        }
      }

      // Add final COMPLETED step if it doesn't exist (for non-BUILD_SUCCEEDED cases)
      const completedStepIndex = this.steps.findIndex(
        step => step.title === this.getDisplayTitleForProgress(StepperState.COMPLETED)
      );

      if (completedStepIndex === -1 && this.steps.length > 0) {
        const description = this.formatDescription(this.progressDescription) || 'Process completed successfully.';
        const completedStep: StepperItem = {
          title: this.getDisplayTitleForProgress(StepperState.COMPLETED),
          description: description,
          visibleDescription: '', // Start empty for typewriter effect
          completed: true, // Mark as completed to show checkmark
          active: true, // Make it active
          isTyping: true,
          startTime: Date.now(),
          elapsedTime: 0,
          timerActive: false // Completed steps don't need active timers
        };

        // Use the helper method to add the completed step
        this.addFinalStep(completedStep);
      }
    } else if (this.status === 'FAILED') {
      // If status is FAILED, handle the failure appropriately
      this.handleFailedStep(this.progress, this.progressDescription);
    }
  }

  /**
   * Handles a failed step, creating or updating it as needed
   * @param progress The progress state that failed
   * @param progressDescription The description of the failure
   */
  private handleFailedStep(progress: string, progressDescription: string): void {
    // First, determine the appropriate failure step title based on the current progress
    let failedStepTitle = '';
    let defaultFailureMessage = 'Process failed.';

    // If it's a build failure or already BUILD_FAILED, use the BUILD_FAILED state
    if (progress === StepperState.BUILD_STARTED || progress === StepperState.BUILD_FAILED) {
      failedStepTitle = this.getDisplayTitleForProgress(StepperState.BUILD_FAILED);
      defaultFailureMessage = 'Build process failed.';
    } else {
      // For other steps, create a custom failure title based on the current step
      // First get the current step's display title
      const currentStepTitle = this.getDisplayTitleForProgress(progress);
      failedStepTitle = `${currentStepTitle} Failed`;
    }

    // For BUILD_FAILED progress, we always want to create a new step
    // This ensures we show "Build Failed" as a separate step with an X icon
    let failedStepIndex = -1;

    // Only look for existing failure steps if it's not BUILD_FAILED progress
    if (progress !== StepperState.BUILD_FAILED) {
      // Check if we already have a failure step
      failedStepIndex = this.steps.findIndex(
        step => step.title === failedStepTitle || this.isFailureStep(step)
      );
    }

    // Set the status to FAILED to ensure the retry button is displayed
    this.status = 'FAILED';

    // Extract error message from the log field if available
    let errorMessage = '';

    // First try to get the error message from the progress description
    if (progressDescription && progressDescription.trim() !== '') {
      errorMessage = this.formatDescription(progressDescription);
    }

    // If we couldn't get an error message from the progress description,
    // try to get it from the polling service's last status response
    if (!errorMessage && this.useApi) {
      const lastResponse = this.pollingService.getLastStatusResponse();
      if (lastResponse && lastResponse.details && lastResponse.details.log) {
        // Extract error message from the log field
        const logContent = lastResponse.details.log;
        errorMessage = this.extractErrorMessage(logContent);
      }
    }

    // If we still don't have an error message, use the default
    const formattedDescription = errorMessage || defaultFailureMessage;

    if (failedStepIndex === -1) {
      // We don't have a failure step yet, so create one
      const failedStep: StepperItem = {
        title: failedStepTitle,
        description: formattedDescription, // Use the formatted description
        visibleDescription: '', // Start empty for typewriter effect
        completed: false,
        active: true,
        isTyping: true,
        retryCount: 0, // Initialize retry count for failed steps
        startTime: Date.now(),
        elapsedTime: 0,
        timerActive: false // Failed steps don't need active timers
      };

      // If we have a current step, mark it appropriately
      if (this.currentStep) {
        this.currentStep.active = false;
        // Ensure the step is not marked as completed to prevent showing checkmark
        this.currentStep.completed = false;

        // Also ensure that all previous steps are properly marked
        for (let i = 0; i < this.steps.length; i++) {
          // If this is the current step or a step that was in progress when failure occurred,
          // mark it as not completed
          if (i === this.currentStepIndex ||
              (this.steps[i].active && !this.steps[i].completed)) {
            this.steps[i].completed = false;
            this.steps[i].active = false;
          }
        }
      }

      // Collapse all steps
      for (let i = 0; i < this.steps.length; i++) {
        this.collapsedSteps.add(i);
      }

      // Add the failed step
      this.steps.push(failedStep);
      this.currentStep = failedStep;
      this.currentStepIndex = this.steps.length - 1;

      // Expand only the failed step
      this.collapsedSteps.delete(this.currentStepIndex);

      // Start the typewriter animation for the failed step
      this.startTypewriterAnimation(this.currentStepIndex);

      this.stepUpdated.emit(this.currentStepIndex);

      // Ensure smooth scrolling to the failed step
      setTimeout(() => {
        const stepElements = document.querySelectorAll('.stepper-item');
        if (stepElements && stepElements.length > 0) {
          const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
          if (lastStep) {
            lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }
      }, 100);
    } else {
      // We already have a failure step, make sure it has the correct description
      const failedStep = this.steps[failedStepIndex];

      // Always update the description for failed steps to ensure it shows the latest error
      failedStep.description = formattedDescription;
      failedStep.visibleDescription = ''; // Reset visible description
      failedStep.isTyping = true; // Set typing state

      // Make this step active
      failedStep.active = true;
      failedStep.completed = false;

      // Update current step reference
      this.currentStep = failedStep;
      this.currentStepIndex = failedStepIndex;

      // Collapse all other steps
      for (let i = 0; i < this.steps.length; i++) {
        if (i !== failedStepIndex) {
          this.collapsedSteps.add(i);
        }
      }

      // Expand the failed step
      this.collapsedSteps.delete(failedStepIndex);

      // Start typewriter animation
      this.startTypewriterAnimation(failedStepIndex);

      // Emit step updated event
      this.stepUpdated.emit(failedStepIndex);
    }
  }

  /**
   * Gets the display title for a progress state
   * @param progress The progress state
   * @returns The display title for the progress state
   */
  public getDisplayTitleForProgress(progress: string): string {
    // Check if this is a known stepper state
    if (Object.values(StepperState).includes(progress as StepperState)) {
      return this.stepperStateMap[progress as StepperState];
    }

    // If not a known state, return the original progress value
    return progress;
  }

  /**
   * Determines if a step is a failure step
   * @param step The step to check
   * @returns True if the step is a failure step, false otherwise
   */
  isFailureStep(step: StepperItem): boolean {
    // Check if the step title contains 'Failed' or matches the BUILD_FAILED display title
    // Also check if the status is FAILED and this is the current step
    return step.title.includes('Failed') ||
           step.title === this.getDisplayTitleForProgress(StepperState.BUILD_FAILED) ||
           (this.status === 'FAILED' && step === this.currentStep);
  }

  /**
   * Handles retry button click for a failed step
   * @param index The index of the step to retry
   */
  onRetryClick(index: number, event?: Event): void {
    // Stop event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    // Get the step
    const step = this.steps[index];

    // Initialize retryCount if it doesn't exist
    if (step.retryCount === undefined) {
      step.retryCount = 0;
    }

    // Increment retry count
    step.retryCount++;

    // Log retry attempt

    // Check if we should enable any special behavior after 3 retries
    if (step.retryCount >= 3) {
      // You could add additional behavior here if needed
    }

    // Change status back to IN_PROGRESS to indicate retry is happening
    this.status = 'IN_PROGRESS';

    // Update the step's properties to show it's being retried
    step.completed = false;
    step.active = true;
    step.isRetrying = true; // Set the isRetrying flag for shimmer effect

    // Expand the step if it's collapsed
    this.collapsedSteps.delete(index);

    // Add a visual indication that retry is happening with shimmer effect
    // The shimmer effect is applied via CSS based on the isRetrying flag

    // Emit retry event with step index after a short delay
    // This matches the behavior in error-page component
    setTimeout(() => {
      // Emit the retry event to the parent component
      this.retryStep.emit(index);

      // Reset the error state in the stepper
      this.resetErrorState();
    }, 500);
  }

  /**
   * Resets the error state in the stepper
   * This is called when a retry is initiated
   */
  private resetErrorState(): void {
    // Change status to IN_PROGRESS
    this.status = 'IN_PROGRESS';

    // Reset any error-related properties
    // Find the step that is currently being retried
    const retryingStepIndex = this.steps.findIndex(step => step.isRetrying);
    if (retryingStepIndex >= 0) {
      // Keep the shimmer effect for a short time to provide visual feedback
      setTimeout(() => {
        // Reset the isRetrying flag after a delay
        this.steps[retryingStepIndex].isRetrying = false;
        // Update the UI to reflect the changes
        this.cdr.detectChanges();
      }, 3000); // Keep shimmer for 3 seconds
    }

    // Update the UI to reflect the changes
    this.cdr.detectChanges();
  }

  /**
   * Checks if a step has reached the maximum retry attempts
   * @param index The index of the step to check
   * @returns True if the step has reached the maximum retry attempts
   */
  hasReachedMaxRetries(index: number): boolean {
    if (index < 0 || index >= this.steps.length) {
      return false;
    }
    const step = this.steps[index];
    // If retryCount is undefined, treat it as 0
    const retryCount = step.retryCount || 0;
    return retryCount >= 3;
  }

  /**
   * Debug method to check if the retry button should be visible
   * @param step The step to check
   * @param index The index of the step
   * @returns True if the retry button should be visible
   */
  shouldShowRetryButton(step: StepperItem, index: number): boolean {
    const isFailure = this.isFailureStep(step);
    const notMaxRetries = !this.hasReachedMaxRetries(index);
    return isFailure && notMaxRetries;
  }

  /**
   * Debug method to create a failed step for testing
   * This method can be called from the parent component to test the retry button
   */
  public createFailedStepForTesting(): void {
    // Set status to FAILED
    this.status = 'FAILED';

    // Create a failed step
    const failedStep: StepperItem = {
      title: 'Test Failed',
      description: 'This is a test failed step for testing the retry button.',
      visibleDescription: 'This is a test failed step for testing the retry button.',
      completed: false,
      active: true,
      isTyping: false,
      retryCount: 0,
      startTime: Date.now(),
      elapsedTime: 0,
      timerActive: false
    };

    // Add the failed step
    this.steps.push(failedStep);
    this.currentStep = failedStep;
    this.currentStepIndex = this.steps.length - 1;

    // Expand only the failed step
    this.collapsedSteps.delete(this.currentStepIndex);

  }

  /**
   * Checks if a step is currently in processing state
   * @param index The index of the step to check
   * @returns True if the step is in processing state
   */
  isProcessingStep(index: number): boolean {
    // A step is processing if:
    // 1. The status is IN_PROGRESS
    // 2. This step is the current step
    // 3. The current step is not completed
    // 4. The step is not a failure step

    // First check if we have a valid current step
    if (!this.currentStep || index < 0 || index >= this.steps.length) {
      return false;
    }

    return (
      this.status === 'IN_PROGRESS' &&
      index === this.currentStepIndex &&
      !this.currentStep.completed &&
      !this.isFailureStep(this.steps[index])
    );
  }

  /**
   * Checks if a step is a processed step (completed but not the current processing step)
   * @param index The index of the step to check
   * @returns True if the step is a processed step
   */
  isProcessedStep(index: number): boolean {
    // A step is processed if:
    // 1. It has a valid index
    // 2. It is completed
    // 3. It is not the current processing step

    if (index < 0 || index >= this.steps.length) {
      return false;
    }

    const step = this.steps[index];
    return step.completed && !this.isProcessingStep(index);
  }

  /**
   * Helper method to add a final step (completed or build succeeded)
   * @param step The step to add
   */
  private addFinalStep(step: StepperItem): void {
    // Mark previous step as completed and collapse it
    const prevStepIndex = this.steps.length - 1;
    this.steps[prevStepIndex].completed = true;
    this.steps[prevStepIndex].active = false;
    // Stop the timer for the previous step and capture completion time
    this.stopStepTimer(prevStepIndex);

    // Ensure only the current step is expanded
    // First, collapse all steps
    for (let i = 0; i < this.steps.length; i++) {
      this.collapsedSteps.add(i);
    }

    // Add the step
    this.steps.push(step);
    this.currentStep = step;
    this.currentStepIndex = this.steps.length - 1;

    // Expand only the new step
    this.collapsedSteps.delete(this.currentStepIndex);

    // Start the typewriter animation for the new step
    this.startTypewriterAnimation(this.currentStepIndex);

    this.stepUpdated.emit(this.currentStepIndex);

    // Ensure smooth scrolling to the new step
    setTimeout(() => {
      const stepElements = document.querySelectorAll('.stepper-item');
      if (stepElements && stepElements.length > 0) {
        const lastStep = stepElements[stepElements.length - 1] as HTMLElement;
        if (lastStep) {
          lastStep.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    }, 100);
  }

  /**
   * Extracts error message from a log field
   * @param logContent The log content to parse
   * @returns The extracted error message or empty string
   */
  private extractErrorMessage(logContent: string): string {
    if (!logContent) return '';

    try {
      // Check if the log content is a JSON string
      if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
        // Try to parse the log content as JSON
        const parsedLog = JSON.parse(logContent);

        // If it has a message property, use that
        if (parsedLog.message) {
          return parsedLog.message;
        }

        // If it has an error property, use that
        if (parsedLog.error) {
          return parsedLog.error;
        }

        // If it has a data property with an error message, use that
        if (parsedLog.data && typeof parsedLog.data === 'string' &&
            (parsedLog.data.includes('error') || parsedLog.data.includes('Error') ||
             parsedLog.data.includes('failed') || parsedLog.data.includes('Failed'))) {
          return parsedLog.data;
        }
      }
    } catch (e) {
    }

    // If we couldn't extract an error message, return the original log content
    return logContent;
  }

  /**
   * Starts the typewriter animation for a step
   * @param stepIndex The index of the step to animate
   */
  private startTypewriterAnimation(stepIndex: number): void {
    if (stepIndex < 0 || stepIndex >= this.steps.length) {
      return;
    }

    const step = this.steps[stepIndex];

    // If the step is already fully typed or not in typing state, don't animate
    if (!step.isTyping || step.visibleDescription === step.description) {
      return;
    }

    // Clear any existing typing animation for this step
    if (this.timeoutRefs[`typing-${stepIndex}`]) {
      clearTimeout(this.timeoutRefs[`typing-${stepIndex}`]);
    }

    // Reset the visible description if it's empty
    if (!step.visibleDescription) {
      step.visibleDescription = '';
    }

    // Function to type one character at a time
    const typeNextChar = () => {
      if (!step.isTyping) {
        return; // Stop if typing was cancelled
      }

      const currentLength = step.visibleDescription.length;
      const fullText = step.description;

      if (currentLength < fullText.length) {
        // Add the next character
        step.visibleDescription = fullText.substring(0, currentLength + 1);

        // Schedule the next character
        this.timeoutRefs[`typing-${stepIndex}`] = setTimeout(typeNextChar, this.typingSpeed);
      } else {
        // Typing is complete
        step.isTyping = false;
      }
    };

    // Start the typing animation
    this.timeoutRefs[`typing-${stepIndex}`] = setTimeout(typeNextChar, this.typingSpeed);
  }

  /**
   * Start the timer for the current active step
   */
  private startTimer(): void {
    // Stop any existing timer
    this.stopTimer();

    // Start a new timer that updates every second
    this.timerInterval = setInterval(() => {
      this.updateTimers();
    }, this.timerUpdateInterval);
  }

  /**
   * Stop the timer
   */
  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update the elapsed time for all active timers
   */
  private updateTimers(): void {
    const currentTime = Date.now();
    let hasActiveTimers = false;

    this.steps.forEach(step => {
      if (step.timerActive && step.startTime) {
        step.elapsedTime = Math.floor((currentTime - step.startTime) / 1000);
        hasActiveTimers = true;
      }
    });

    // Stop the timer if no steps have active timers
    if (!hasActiveTimers) {
      this.stopTimer();
    }

    // Trigger change detection to update the UI
    this.cdr.detectChanges();
  }

  /**
   * Stop the timer for a specific step and capture completion time
   */
  private stopStepTimer(stepIndex: number): void {
    if (stepIndex >= 0 && stepIndex < this.steps.length) {
      const step = this.steps[stepIndex];
      if (step.timerActive && step.startTime) {
        // Capture the final completion time
        step.completionTime = Math.floor((Date.now() - step.startTime) / 1000);
        step.elapsedTime = step.completionTime; // Ensure elapsedTime matches completion time
      }
      step.timerActive = false;
    }
  }

  /**
   * Format elapsed time into a readable string (M:SS format like 0:06)
   */
  formatElapsedTime(elapsedTime: number): string {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
