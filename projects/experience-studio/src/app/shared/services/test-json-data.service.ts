import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, map } from 'rxjs';

export interface TestJsonStep {
  step?: number;
  step_name?: string;
  status_code: number;
  details: {
    status: string;
    log: string;
    progress: string;
    progress_description: string;
    history: any[];
    metadata: any;
  };
}

export interface StepperDataFromJson {
  title: string;
  description: string;
  status: string;
  progress: string;
  agent: string;
  action: string;
  timestamp?: string;
  metadata?: any;
}

@Injectable({
  providedIn: 'root'
})
export class TestJsonDataService {
  private currentStepSubject = new BehaviorSubject<number>(0);
  private stepsDataSubject = new BehaviorSubject<StepperDataFromJson[]>([]);
  private isPlayingSubject = new BehaviorSubject<boolean>(false);
  private playIntervalId: any;

  public currentStep$ = this.currentStepSubject.asObservable();
  public stepsData$ = this.stepsDataSubject.asObservable();
  public isPlaying$ = this.isPlayingSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Load test.json data and transform it to stepper format
   */
  loadTestJsonData(): Observable<StepperDataFromJson[]> {
    return this.http.get<TestJsonStep[]>('/test.json').pipe(
      map(jsonData => this.transformJsonToStepperData(jsonData))
    );
  }

  /**
   * Transform JSON data to stepper-compatible format
   */
  private transformJsonToStepperData(jsonData: TestJsonStep[]): StepperDataFromJson[] {
    return jsonData.map((step, index) => {
      const agentInfo = this.extractAgentInfo(step.details.log);
      const title = this.getStepTitle(step.details.progress, index + 1);
      
      return {
        title,
        description: step.details.progress_description || 'Processing...',
        status: step.details.status,
        progress: step.details.progress,
        agent: agentInfo.agent,
        action: agentInfo.action,
        timestamp: this.generateTimestamp(index),
        metadata: step.details.metadata
      };
    });
  }

  /**
   * Extract agent and action information from log string
   */
  private extractAgentInfo(log: string): { agent: string; action: string } {
    const agentMatch = log.match(/Agent\s*:\s*([^|]+)/);
    const actionMatch = log.match(/Action\s*:\s*(.+)/);
    
    return {
      agent: agentMatch ? agentMatch[1].trim() : 'Code Agent',
      action: actionMatch ? actionMatch[1].trim() : 'Processing'
    };
  }

  /**
   * Generate user-friendly step titles based on progress
   */
  private getStepTitle(progress: string, stepNumber: number): string {
    const titleMap: { [key: string]: string } = {
      'OVERVIEW': 'Requirements Analysis',
      'SEED_PROJECT_INITIALIZED': 'Project Setup',
      'FILE_QUEUE': 'File Planning',
      'DESIGN_SYSTEM_MAPPED': 'Design System Creation',
      'COMPONENTS_CREATED': 'Component Generation',
      'LAYOUT_ANALYZED': 'Layout Analysis',
      'PAGES_GENERATED': 'Page Generation',
      'DEPLOY': 'Deployment'
    };

    return titleMap[progress] || `Step ${stepNumber}`;
  }

  /**
   * Generate realistic timestamps for steps
   */
  private generateTimestamp(index: number): string {
    const now = new Date();
    const stepTime = new Date(now.getTime() - (index * 30000)); // 30 seconds apart
    return stepTime.toLocaleTimeString();
  }

  /**
   * Initialize stepper with test data
   */
  initializeStepperData(): void {
    this.loadTestJsonData().subscribe(data => {
      this.stepsDataSubject.next(data);
      this.currentStepSubject.next(0);
    });
  }

  /**
   * Move to next step
   */
  nextStep(): void {
    const currentStep = this.currentStepSubject.value;
    const stepsData = this.stepsDataSubject.value;
    
    if (currentStep < stepsData.length - 1) {
      this.currentStepSubject.next(currentStep + 1);
    }
  }

  /**
   * Move to previous step
   */
  previousStep(): void {
    const currentStep = this.currentStepSubject.value;
    
    if (currentStep > 0) {
      this.currentStepSubject.next(currentStep - 1);
    }
  }

  /**
   * Go to specific step
   */
  goToStep(stepIndex: number): void {
    const stepsData = this.stepsDataSubject.value;
    
    if (stepIndex >= 0 && stepIndex < stepsData.length) {
      this.currentStepSubject.next(stepIndex);
    }
  }

  /**
   * Start auto-play mode
   */
  startAutoPlay(intervalMs: number = 3000): void {
    if (this.isPlayingSubject.value) {
      return; // Already playing
    }

    this.isPlayingSubject.next(true);
    
    this.playIntervalId = setInterval(() => {
      const currentStep = this.currentStepSubject.value;
      const stepsData = this.stepsDataSubject.value;
      
      if (currentStep < stepsData.length - 1) {
        this.nextStep();
      } else {
        this.stopAutoPlay();
        // Optionally restart from beginning
        setTimeout(() => {
          this.currentStepSubject.next(0);
        }, 1000);
      }
    }, intervalMs);
  }

  /**
   * Stop auto-play mode
   */
  stopAutoPlay(): void {
    if (this.playIntervalId) {
      clearInterval(this.playIntervalId);
      this.playIntervalId = null;
    }
    this.isPlayingSubject.next(false);
  }

  /**
   * Reset stepper to beginning
   */
  reset(): void {
    this.stopAutoPlay();
    this.currentStepSubject.next(0);
  }

  /**
   * Get current step data
   */
  getCurrentStepData(): StepperDataFromJson | null {
    const currentStep = this.currentStepSubject.value;
    const stepsData = this.stepsDataSubject.value;
    
    return stepsData[currentStep] || null;
  }

  /**
   * Get all steps data
   */
  getAllStepsData(): StepperDataFromJson[] {
    return this.stepsDataSubject.value;
  }

  /**
   * Get current step index
   */
  getCurrentStepIndex(): number {
    return this.currentStepSubject.value;
  }
}
