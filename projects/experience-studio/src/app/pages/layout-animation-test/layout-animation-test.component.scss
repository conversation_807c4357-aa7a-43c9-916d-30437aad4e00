.layout-animation-test-container {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;

  &.dark-theme {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #e9ecef;
  }

  .header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: #495057;
      text-shadow: 0 2px 10px rgba(0,0,0,0.1);

      .dark-theme & {
        color: #e9ecef;
      }
    }

    p {
      font-size: 1.1rem;
      color: #6c757d;
      margin-bottom: 2rem;

      .dark-theme & {
        color: #adb5bd;
      }
    }

    .controls {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;

      .control-btn {
        padding: 0.75rem 1.5rem;
        border: 2px solid #dee2e6;
        border-radius: 25px;
        background: rgba(255, 255, 255, 0.9);
        color: #495057;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
          border-color: #c7d2fe;
        }

        &.active {
          background: #c7d2fe;
          border-color: #a5b4fc;
          color: #4c1d95;
        }

        .dark-theme & {
          background: rgba(30, 30, 30, 0.9);
          border-color: #495057;
          color: #e9ecef;

          &:hover {
            border-color: #6d28d9;
          }

          &.active {
            background: #4c1d95;
            border-color: #6d28d9;
            color: #e9ecef;
          }
        }
      }
    }
  }

  .layouts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;

    .layout-item {
      height: 400px;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-5px);
      }

      .dark-theme & {
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      }
    }
  }

  .info-section {
    margin-bottom: 4rem;

    h2 {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
      color: #495057;

      .dark-theme & {
        color: #e9ecef;
      }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;

      .feature-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .dark-theme & {
          background: rgba(30, 30, 30, 0.9);
          border: 1px solid rgba(255, 255, 255, 0.1);

          &:hover {
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          }
        }

        h3 {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          color: #495057;

          .dark-theme & {
            color: #e9ecef;
          }
        }

        p {
          color: #6c757d;
          line-height: 1.6;

          .dark-theme & {
            color: #adb5bd;
          }
        }
      }
    }
  }

  .usage-section {
    h2 {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
      color: #495057;

      .dark-theme & {
        color: #e9ecef;
      }
    }

    .code-example {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(0, 0, 0, 0.05);
      border-radius: 15px;
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;

      .dark-theme & {
        background: rgba(30, 30, 30, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      pre {
        margin: 0;
        overflow-x: auto;

        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.9rem;
          line-height: 1.6;
          color: #495057;

          .dark-theme & {
            color: #e9ecef;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .layout-animation-test-container {
    padding: 1rem;

    .header {
      h1 {
        font-size: 2rem;
      }

      .controls {
        gap: 0.5rem;

        .control-btn {
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
        }
      }
    }

    .layouts-grid {
      grid-template-columns: 1fr;
      gap: 1rem;

      .layout-item {
        height: 300px;
      }
    }

    .info-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-card {
          padding: 1rem;
        }
      }
    }

    .usage-section {
      .code-example {
        padding: 1rem;

        pre code {
          font-size: 0.8rem;
        }
      }
    }
  }
}
