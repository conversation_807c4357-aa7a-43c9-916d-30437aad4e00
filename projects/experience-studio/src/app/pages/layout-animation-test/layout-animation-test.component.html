<div class="layout-animation-test-container" [class.dark-theme]="currentTheme === 'dark'">
  <div class="header">
    <h1>🎨 Fluid Layout Animation Test</h1>
    <p>Testing the new fluid layout animation component that replaces shimmer effects</p>
    
    <div class="controls">
      <button class="control-btn" (click)="toggleTheme()">
        {{ currentTheme === 'light' ? '🌙 Dark' : '☀️ Light' }} Theme
      </button>
      <button class="control-btn" [class.active]="showTitles" (click)="toggleTitles()">
        {{ showTitles ? '✅' : '❌' }} Titles
      </button>
      <button class="control-btn" [class.active]="showDescriptions" (click)="toggleDescriptions()">
        {{ showDescriptions ? '✅' : '❌' }} Descriptions
      </button>
      <button class="control-btn" [class.active]="animated" (click)="toggleAnimation()">
        {{ animated ? '✅' : '❌' }} Animation
      </button>
    </div>
  </div>

  <div class="layouts-grid">
    <div *ngFor="let layout of layouts" class="layout-item">
      <app-fluid-layout-animation
        [layoutKey]="layout.key"
        [theme]="currentTheme"
        [animated]="animated"
        [showTitle]="showTitles"
        [showDescription]="showDescriptions">
      </app-fluid-layout-animation>
    </div>
  </div>

  <div class="info-section">
    <h2>🔍 Component Features</h2>
    <div class="features-grid">
      <div class="feature-card">
        <h3>🎯 Layout Types</h3>
        <p>Supports all 8 layout configurations from HB to HLSBRSF with accurate visual representation</p>
      </div>
      <div class="feature-card">
        <h3>🌊 Fluid Animations</h3>
        <p>Smooth sidebar animations that communicate layout structure and responsive behavior</p>
      </div>
      <div class="feature-card">
        <h3>🎨 Theme Support</h3>
        <p>Full light and dark theme support with appropriate color schemes and contrast</p>
      </div>
      <div class="feature-card">
        <h3>📱 Responsive Design</h3>
        <p>Adapts to different screen sizes and container dimensions automatically</p>
      </div>
      <div class="feature-card">
        <h3>⚡ Performance</h3>
        <p>Hardware accelerated animations with optimized rendering for smooth performance</p>
      </div>
      <div class="feature-card">
        <h3>🔧 Configurable</h3>
        <p>Toggle titles, descriptions, and animations based on use case requirements</p>
      </div>
    </div>
  </div>

  <div class="usage-section">
    <h2>💻 Usage Example</h2>
    <div class="code-example">
      <pre><code>&lt;app-fluid-layout-animation
  [layoutKey]="'{{ layouts[0].key }}'"
  [theme]="'{{ currentTheme }}'"
  [animated]="{{ animated }}"
  [showTitle]="{{ showTitles }}"
  [showDescription]="{{ showDescriptions }}"&gt;
&lt;/app-fluid-layout-animation&gt;</code></pre>
    </div>
  </div>
</div>
