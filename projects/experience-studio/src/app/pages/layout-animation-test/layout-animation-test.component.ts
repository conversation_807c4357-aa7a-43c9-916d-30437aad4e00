import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FluidLayoutAnimationComponent } from '../../shared/components/fluid-layout-animation/fluid-layout-animation.component';

@Component({
  selector: 'app-layout-animation-test',
  standalone: true,
  imports: [CommonModule, FluidLayoutAnimationComponent],
  templateUrl: './layout-animation-test.component.html',
  styleUrls: ['./layout-animation-test.component.scss']
})
export class LayoutAnimationTestComponent {
  layouts = [
    { key: 'HB', name: 'Header + Body' },
    { key: 'HBF', name: 'Header + Body + Footer' },
    { key: 'HLSB', name: 'Header + Left Sidebar + Body' },
    { key: 'HLSBF', name: 'Header + Left Sidebar + Body + Footer' },
    { key: 'HBRS', name: 'Header + Body + Right Sidebar' },
    { key: 'HBRSF', name: 'Header + Body + Right Sidebar + Footer' },
    { key: 'HLSBRS', name: 'Header + Left + Body + Right Sidebar' },
    { key: 'HLSBRSF', name: 'Complete Layout' }
  ];

  currentTheme: 'light' | 'dark' = 'light';
  showTitles = true;
  showDescriptions = true;
  animated = true;

  toggleTheme(): void {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
  }

  toggleTitles(): void {
    this.showTitles = !this.showTitles;
  }

  toggleDescriptions(): void {
    this.showDescriptions = !this.showDescriptions;
  }

  toggleAnimation(): void {
    this.animated = !this.animated;
  }
}
