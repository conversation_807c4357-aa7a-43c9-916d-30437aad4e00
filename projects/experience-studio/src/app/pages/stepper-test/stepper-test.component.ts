import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { VerticalStepperComponent } from '../../shared/components/vertical-stepper/vertical-stepper.component';
import { TestJsonDataService, StepperDataFromJson } from '../../shared/services/test-json-data.service';

@Component({
  selector: 'app-stepper-test',
  standalone: true,
  imports: [CommonModule, VerticalStepperComponent],
  template: `
    <div class="stepper-test-container">
      <div class="header">
        <h1>🧪 Vertical Stepper Test with test.json Data</h1>
        <p>Testing the vertical stepper component with real API response data from test.json</p>
      </div>

      <div class="controls">
        <div class="control-group">
          <button 
            class="control-btn primary"
            (click)="startAutoPlay()"
            [disabled]="isPlaying">
            ▶️ Auto Play
          </button>
          <button 
            class="control-btn secondary"
            (click)="stopAutoPlay()"
            [disabled]="!isPlaying">
            ⏸️ Pause
          </button>
          <button 
            class="control-btn"
            (click)="reset()">
            🔄 Reset
          </button>
        </div>

        <div class="control-group">
          <button 
            class="control-btn"
            (click)="previousStep()"
            [disabled]="currentStepIndex <= 0">
            ⬅️ Previous
          </button>
          <span class="step-indicator">
            Step {{ currentStepIndex + 1 }} of {{ totalSteps }}
          </span>
          <button 
            class="control-btn"
            (click)="nextStep()"
            [disabled]="currentStepIndex >= totalSteps - 1">
            ➡️ Next
          </button>
        </div>
      </div>

      <div class="stepper-container">
        <app-vertical-stepper
          [progress]="currentProgress"
          [progressDescription]="currentProgressDescription"
          [status]="currentStatus"
          [theme]="'light'"
          [restartable]="false"
          [useApi]="false"
          (stepUpdated)="onStepUpdated($event)"
          (retryStep)="onRetryStep($event)">
        </app-vertical-stepper>
      </div>

      <div class="debug-info" *ngIf="showDebugInfo">
        <h3>🔍 Debug Information</h3>
        <div class="debug-content">
          <div class="debug-item">
            <strong>Current Step:</strong> {{ currentStepIndex + 1 }}
          </div>
          <div class="debug-item">
            <strong>Progress:</strong> {{ currentProgress }}
          </div>
          <div class="debug-item">
            <strong>Status:</strong> {{ currentStatus }}
          </div>
          <div class="debug-item">
            <strong>Agent:</strong> {{ currentStepData?.agent }}
          </div>
          <div class="debug-item">
            <strong>Action:</strong> {{ currentStepData?.action }}
          </div>
          <div class="debug-item">
            <strong>Description:</strong> {{ currentProgressDescription }}
          </div>
        </div>
      </div>

      <div class="toggle-debug">
        <button 
          class="control-btn small"
          (click)="toggleDebugInfo()">
          {{ showDebugInfo ? '🙈 Hide' : '👁️ Show' }} Debug Info
        </button>
      </div>
    </div>
  `,
  styles: [`
    .stepper-test-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 10px;
    }

    .header h1 {
      margin: 0 0 10px 0;
      font-size: 2rem;
    }

    .header p {
      margin: 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 10px;
      border: 1px solid #e9ecef;
    }

    .control-group {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .control-btn {
      padding: 8px 16px;
      border: 2px solid #299CDB;
      background: white;
      color: #299CDB;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      font-size: 14px;
    }

    .control-btn:hover:not(:disabled) {
      background: #299CDB;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(41, 156, 219, 0.3);
    }

    .control-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .control-btn.primary {
      background: #28a745;
      border-color: #28a745;
      color: white;
    }

    .control-btn.primary:hover:not(:disabled) {
      background: #218838;
      border-color: #218838;
    }

    .control-btn.secondary {
      background: #dc3545;
      border-color: #dc3545;
      color: white;
    }

    .control-btn.secondary:hover:not(:disabled) {
      background: #c82333;
      border-color: #c82333;
    }

    .control-btn.small {
      padding: 6px 12px;
      font-size: 12px;
    }

    .step-indicator {
      padding: 8px 16px;
      background: #e9ecef;
      border-radius: 20px;
      font-weight: 600;
      color: #495057;
    }

    .stepper-container {
      background: white;
      border-radius: 10px;
      padding: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
    }

    .debug-info {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }

    .debug-info h3 {
      margin: 0 0 15px 0;
      color: #495057;
    }

    .debug-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 10px;
    }

    .debug-item {
      padding: 8px 12px;
      background: white;
      border-radius: 5px;
      border: 1px solid #dee2e6;
      font-size: 14px;
    }

    .debug-item strong {
      color: #299CDB;
    }

    .toggle-debug {
      text-align: center;
    }

    @media (max-width: 768px) {
      .controls {
        flex-direction: column;
        gap: 15px;
      }

      .control-group {
        flex-wrap: wrap;
        justify-content: center;
      }

      .debug-content {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class StepperTestComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Component state
  currentStepIndex = 0;
  totalSteps = 0;
  currentProgress = '';
  currentProgressDescription = '';
  currentStatus = 'PENDING';
  currentStepData: StepperDataFromJson | null = null;
  isPlaying = false;
  showDebugInfo = false;

  constructor(private testJsonDataService: TestJsonDataService) {}

  ngOnInit(): void {
    this.initializeData();
    this.subscribeToDataChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.testJsonDataService.stopAutoPlay();
  }

  private initializeData(): void {
    this.testJsonDataService.initializeStepperData();
  }

  private subscribeToDataChanges(): void {
    // Subscribe to current step and steps data changes
    combineLatest([
      this.testJsonDataService.currentStep$,
      this.testJsonDataService.stepsData$,
      this.testJsonDataService.isPlaying$
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([currentStep, stepsData, isPlaying]) => {
      this.currentStepIndex = currentStep;
      this.totalSteps = stepsData.length;
      this.isPlaying = isPlaying;

      if (stepsData.length > 0 && currentStep < stepsData.length) {
        this.currentStepData = stepsData[currentStep];
        this.currentProgress = this.currentStepData.progress;
        this.currentProgressDescription = this.currentStepData.description;
        this.currentStatus = this.currentStepData.status;
      }
    });
  }

  // Control methods
  startAutoPlay(): void {
    this.testJsonDataService.startAutoPlay(3000); // 3 seconds interval
  }

  stopAutoPlay(): void {
    this.testJsonDataService.stopAutoPlay();
  }

  reset(): void {
    this.testJsonDataService.reset();
  }

  nextStep(): void {
    this.testJsonDataService.nextStep();
  }

  previousStep(): void {
    this.testJsonDataService.previousStep();
  }

  goToStep(stepIndex: number): void {
    this.testJsonDataService.goToStep(stepIndex);
  }

  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
  }

  // Stepper event handlers
  onStepUpdated(stepIndex: number): void {
    console.log('Stepper step updated:', stepIndex);
  }

  onRetryStep(stepIndex: number): void {
    console.log('Stepper retry step:', stepIndex);
  }
}
