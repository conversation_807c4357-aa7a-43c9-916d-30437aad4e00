import { Routes } from '@angular/router';

export const EXPERIENCE_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./experience.component').then(m => m.ExperienceComponent),
    children: [
      {
        path: 'main',
        loadComponent: () =>
          import('../shared/components/landing-page/landing-page.component').then(
            m => m.LandingPageComponent
          ),
        outlet: 'primary',
        data: { preload: true }
      },
      {
        path: 'image-to-code',
        outlet: 'primary',
        loadChildren: () =>
          import('./image-to-code/image-to-code-routing').then(m => m.IMAGE_TO_CODE_ROUTES),
        data: { preload: true }
      },
      {
        path: 'design-analysis',
        outlet: 'primary',
        loadChildren: () =>
          import('./design-analysis/design-analysis-routing').then(m => m.DESIGN_ANALYSIS_ROUTES),
      },
      {
        path: 'prompt-to-code',
        outlet: 'primary',
        loadChildren: () =>
          import('./prompt-to-code/prompt-to-code.routing').then(m => m.PROMPT_TO_CODE_ROUTES),
      },
      {
        path: 'layout-shimmer-demo',
        outlet: 'primary',
        loadComponent: () =>
          import('./layout-shimmer-demo/layout-shimmer-demo.component').then(m => m.LayoutShimmerDemoComponent),
      },
      {
        path: 'layout-animation-test',
        outlet: 'primary',
        loadComponent: () =>
          import('./layout-animation-test/layout-animation-test.component').then(m => m.LayoutAnimationTestComponent),
      },
      {
        path: '',
        redirectTo: 'main',
        pathMatch: 'full',
      },
    ],
  },
];
